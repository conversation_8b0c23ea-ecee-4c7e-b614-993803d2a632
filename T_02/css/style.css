/* 全力以赴信息科技 - 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-scrolled {
    background-color: rgba(13, 110, 253, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--warning-color) !important;
}

.navbar-nav .nav-link.active {
    color: var(--warning-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--warning-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

/* 首页横幅样式 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-image {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* 章节标题样式 */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    position: relative;
    margin-bottom: 1rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-color);
}

/* 特色框样式 */
.feature-box {
    padding: 2rem 1rem;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-box i {
    font-size: 3rem;
}

/* 服务卡片样式 */
.service-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.service-card .card-body {
    padding: 2.5rem 2rem;
}

.service-card i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

/* 产品卡片样式 */
.product-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-card .card-body {
    padding: 2rem;
}

.product-icon i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.product-card ul li {
    padding: 0.3rem 0;
    font-size: 0.9rem;
}

/* 新闻卡片样式 */
.news-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.news-card .card-body {
    padding: 2rem;
}

.news-date {
    font-size: 0.9rem;
    font-weight: 500;
}

.news-card .card-title a {
    color: var(--dark-color);
    transition: color 0.3s ease;
}

.news-card .card-title a:hover {
    color: var(--primary-color);
}

/* 团队卡片样式 */
.team-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    padding: 2rem;
    height: 100%;
}

.team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.team-avatar i {
    font-size: 5rem;
    color: var(--primary-color);
}

.team-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.team-position {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.team-desc {
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.team-skills .badge {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

/* 联系表单样式 */
.contact-form {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
}

.contact-info {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
    height: fit-content;
}

.contact-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item i {
    font-size: 1.5rem;
    width: 40px;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #4c84ff);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #3d73ff);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #ffcd39);
    border: none;
    color: var(--dark-color);
}

.btn-warning:hover {
    background: linear-gradient(45deg, #e0a800, #ffb700);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
    color: var(--dark-color);
}

/* 页脚样式 */
footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

footer h5, footer h6 {
    color: white;
    font-weight: 600;
}

footer ul li {
    margin-bottom: 0.5rem;
}

footer ul li a {
    transition: color 0.3s ease;
}

footer ul li a:hover {
    color: var(--warning-color) !important;
}

.footer-social a {
    font-size: 1.5rem;
    transition: color 0.3s ease, transform 0.3s ease;
}

.footer-social a:hover {
    color: var(--warning-color) !important;
    transform: translateY(-3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .service-card .card-body,
    .product-card .card-body,
    .news-card .card-body {
        padding: 1.5rem;
    }

    .contact-form,
    .contact-info {
        padding: 1.5rem;
    }

    .team-card {
        padding: 1.5rem;
    }
}

/* 滚动行为 */
html {
    scroll-behavior: smooth;
}

/* 加载动画 */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0b5ed7;
}
