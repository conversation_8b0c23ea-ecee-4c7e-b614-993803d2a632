/* 全力以赴信息科技 - 现代酷炫样式 */

/* 全局样式 */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #0f0f23;
    --dark-secondary: #1a1a2e;
    --dark-tertiary: #16213e;
    --light-color: #f8fafc;
    --text-primary: #e2e8f0;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
    --border-color: #334155;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--dark-color);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* 背景动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* 导航栏样式 */
.navbar {
    background: rgba(15, 15, 35, 0.8) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar-scrolled {
    background: rgba(15, 15, 35, 0.95) !important;
    backdrop-filter: blur(30px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary) !important;
    text-decoration: none;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: var(--text-primary) !important;
    background: var(--glass-bg);
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background: rgba(99, 102, 241, 0.1);
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    border-radius: 8px;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.navbar-nav .nav-link:hover::before {
    opacity: 0.1;
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
    background: var(--glass-bg);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px var(--primary-color);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28226, 232, 240, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* 首页横幅样式 */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--dark-color);
    padding-top: 80px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.2) 0%, transparent 70%);
    z-index: 1;
    animation: heroGlow 8s ease-in-out infinite;
}

@keyframes heroGlow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGlow 3s ease-in-out infinite;
}

@keyframes textGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
}

.hero-section .lead {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
}

.hero-image {
    position: relative;
    animation: float 6s ease-in-out infinite;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    opacity: 0.2;
    animation: pulse 4s ease-in-out infinite;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(2deg); }
    66% { transform: translateY(-10px) rotate(-2deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.2; }
    50% { transform: scale(1.1); opacity: 0.3; }
}

.hero-image i {
    font-size: 8rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.5));
}

/* 章节标题样式 */
.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: var(--text-primary);
    position: relative;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
}

/* 按钮样式 */
.btn {
    border-radius: 12px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-lg {
    padding: 1.25rem 3rem;
    font-size: 1.1rem;
    border-radius: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
    border: 1px solid rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(99, 102, 241, 0.4);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #f97316);
    color: var(--dark-color);
    box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f97316, var(--warning-color));
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(245, 158, 11, 0.4);
    color: var(--dark-color);
}

.btn-outline-light {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(99, 102, 241, 0.2);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

/* 特色框样式 */
.feature-box {
    padding: 2rem 1rem;
    border-radius: 16px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.feature-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.feature-box:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(99, 102, 241, 0.2);
    border-color: var(--primary-color);
}

.feature-box:hover::before {
    opacity: 0.05;
}

.feature-box i {
    font-size: 3rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 10px rgba(99, 102, 241, 0.3));
}

.feature-box h5 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 1rem 0 0.5rem;
}

.feature-box p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 服务卡片样式 */
.service-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    height: 100%;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.service-card:hover {
    transform: translateY(-12px) rotateX(5deg);
    box-shadow: 0 25px 80px rgba(99, 102, 241, 0.25);
    border-color: var(--primary-color);
}

.service-card:hover::before {
    opacity: 0.03;
}

.service-card .card-body {
    padding: 3rem 2rem;
    position: relative;
    z-index: 1;
}

.service-card i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 15px rgba(99, 102, 241, 0.4));
    transition: all 0.3s ease;
}

.service-card:hover i {
    transform: scale(1.1) rotateY(10deg);
    filter: drop-shadow(0 0 25px rgba(99, 102, 241, 0.6));
}

.service-card h5 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 产品卡片样式 */
.product-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    height: 100%;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 30px 80px rgba(6, 182, 212, 0.2);
    border-color: var(--accent-color);
}

.product-card:hover::before {
    opacity: 0.03;
}

.product-card .card-body {
    padding: 2.5rem 2rem;
    position: relative;
    z-index: 1;
}

.product-icon i {
    font-size: 4rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 15px rgba(6, 182, 212, 0.4));
    transition: all 0.3s ease;
}

.product-card:hover .product-icon i {
    transform: scale(1.1) rotateZ(5deg);
    filter: drop-shadow(0 0 25px rgba(6, 182, 212, 0.6));
}

.product-card h5 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.product-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.product-card ul li {
    padding: 0.5rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
}

.product-card ul li i {
    font-size: 1rem;
    margin-right: 0.5rem;
    color: var(--success-color);
}

/* 新闻卡片样式 */
.news-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    height: 100%;
}

.news-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--warning-color), var(--primary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.news-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 70px rgba(245, 158, 11, 0.15);
    border-color: var(--warning-color);
}

.news-card:hover::before {
    opacity: 0.03;
}

.news-card .card-body {
    padding: 2.5rem 2rem;
    position: relative;
    z-index: 1;
}

.news-date {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--warning-color);
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.news-date i {
    margin-right: 0.5rem;
}

.news-card .card-title {
    margin-bottom: 1rem;
}

.news-card .card-title a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    display: block;
}

.news-card .card-title a:hover {
    color: var(--warning-color);
    transform: translateX(5px);
}

.news-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* 团队卡片样式 */
.team-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 2.5rem 2rem;
    height: 100%;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--success-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.team-card:hover {
    transform: translateY(-10px) rotateY(5deg);
    box-shadow: 0 30px 80px rgba(16, 185, 129, 0.2);
    border-color: var(--success-color);
}

.team-card:hover::before {
    opacity: 0.03;
}

.team-avatar {
    position: relative;
    margin-bottom: 1.5rem;
}

.team-avatar i {
    font-size: 5rem;
    background: linear-gradient(135deg, var(--success-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 20px rgba(16, 185, 129, 0.4));
    transition: all 0.3s ease;
}

.team-card:hover .team-avatar i {
    transform: scale(1.1) rotateZ(10deg);
    filter: drop-shadow(0 0 30px rgba(16, 185, 129, 0.6));
}

.team-name {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.team-position {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--success-color);
}

.team-desc {
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.team-skills .badge {
    font-size: 0.8rem;
    margin: 0.25rem;
    padding: 0.5rem 1rem;
    background: var(--glass-bg);
    color: var(--text-secondary);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.team-skills .badge:hover {
    background: var(--success-color);
    color: white;
    transform: translateY(-2px);
}

/* 联系表单样式 */
.contact-form {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

.contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0.02;
    z-index: -1;
}

.contact-form h4 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 2rem;
}

.contact-info {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
    height: fit-content;
    position: relative;
    overflow: hidden;
}

.contact-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-color), var(--success-color));
    opacity: 0.02;
    z-index: -1;
}

.contact-info h4 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 2rem;
}

.contact-item {
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--glass-border);
    transition: all 0.3s ease;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item:hover {
    transform: translateX(5px);
}

.contact-item i {
    font-size: 1.5rem;
    width: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.contact-item h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: var(--text-secondary);
    margin: 0;
}

.form-control, .form-select {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-control:focus, .form-select:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.social-links a {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* 按钮样式 */
.btn {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #4c84ff);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #3d73ff);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #ffcd39);
    border: none;
    color: var(--dark-color);
}

.btn-warning:hover {
    background: linear-gradient(45deg, #e0a800, #ffb700);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
    color: var(--dark-color);
}

/* 页脚样式 */
footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

footer h5, footer h6 {
    color: white;
    font-weight: 600;
}

footer ul li {
    margin-bottom: 0.5rem;
}

footer ul li a {
    transition: color 0.3s ease;
}

footer ul li a:hover {
    color: var(--warning-color) !important;
}

.footer-social a {
    font-size: 1.5rem;
    transition: color 0.3s ease, transform 0.3s ease;
}

.footer-social a:hover {
    color: var(--warning-color) !important;
    transform: translateY(-3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .service-card .card-body,
    .product-card .card-body,
    .news-card .card-body {
        padding: 1.5rem;
    }

    .contact-form,
    .contact-info {
        padding: 1.5rem;
    }

    .team-card {
        padding: 1.5rem;
    }
}

/* 滚动行为 */
html {
    scroll-behavior: smooth;
}

/* 加载动画 */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-secondary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* 通用样式改进 */
section {
    position: relative;
    overflow: hidden;
}

.text-muted {
    color: var(--text-secondary) !important;
}

/* 页脚样式更新 */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--dark-tertiary) 100%);
    border-top: 1px solid var(--glass-border);
}

footer h5, footer h6 {
    color: var(--text-primary);
    font-weight: 600;
}

footer p, footer li {
    color: var(--text-secondary);
}

footer ul li a {
    color: var(--text-secondary);
    transition: all 0.3s ease;
    text-decoration: none;
}

footer ul li a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.footer-social a {
    font-size: 1.5rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    margin-right: 1rem;
    text-decoration: none;
}

.footer-social a:hover {
    color: var(--primary-color);
    transform: translateY(-3px) scale(1.1);
}

/* 动画增强 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.6s ease-out;
}

/* 响应式改进 */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 100px;
        text-align: center;
    }

    .hero-section h1 {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }

    .hero-section .lead {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .hero-image {
        margin-top: 2rem;
    }

    .service-card,
    .product-card,
    .news-card,
    .team-card {
        margin-bottom: 2rem;
    }

    .contact-form,
    .contact-info {
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .navbar-nav .nav-link {
        padding: 1rem 1.5rem;
        margin: 0.25rem 0;
        border-radius: 8px;
    }
}

@media (max-width: 576px) {
    .section-title {
        font-size: 2rem;
    }

    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .hero-section .d-flex {
        flex-direction: column;
        gap: 1rem;
    }

    .service-card .card-body,
    .product-card .card-body,
    .news-card .card-body {
        padding: 2rem 1.5rem;
    }

    .team-card {
        padding: 2rem 1.5rem;
    }
}

/* 性能优化 */
.service-card,
.product-card,
.news-card,
.team-card,
.feature-box {
    will-change: transform;
}

/* 可访问性改进 */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .social-links {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .section-title {
        color: black !important;
    }
}
